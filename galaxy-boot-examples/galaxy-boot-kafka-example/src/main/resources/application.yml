server:
  port: 8090

spring:
  application:
    name: galaxy-boot-kafka-example-service

  # Spring Kafka 标准配置
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      group-id: kafka-example-group
      enable-auto-commit: true
    producer:
      batch-size: 16384
      properties:
        linger.ms: 5
        compression.type: none
    listener:
      type: batch  # 覆盖Galaxy默认的batch模式

galaxy:
  system:
    code: KFE

  log:
    request-response:
      enabled: true
      request-headers: true
    performance:
      enabled: true
    default-category: APP_LOG
    exception-pretty-print: true

  # Galaxy 特有配置（最小化）
  kafka:
    enabled: true
    log:
      enabled: true
      max-batch-detail-count: 10

logging:
  level:
    cn.com.chinastock.cnf.kafka.examples: DEBUG
    org.springframework.kafka: ERROR
    org.apache.kafka: ERROR

app:
  id: galaxy-boot
apollo:
  config-service: http://localhost:8080


# 启用Spring Boot Actuator和Tracing
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  tracing:
    sampling:
      probability: 1.0  # 100%采样率，用于演示
  observations:
    key-values:
      application: galaxy-boot-kafka-example
