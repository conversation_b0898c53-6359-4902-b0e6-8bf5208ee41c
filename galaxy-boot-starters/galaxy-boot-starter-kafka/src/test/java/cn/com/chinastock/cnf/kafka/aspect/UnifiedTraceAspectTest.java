package cn.com.chinastock.cnf.kafka.aspect;

import cn.com.chinastock.cnf.kafka.annotation.SingleTraceForBatch;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * UnifiedTraceAspect 测试类
 * 
 * <AUTHOR> Boot Team
 */
@ExtendWith(MockitoExtension.class)
class UnifiedTraceAspectTest {

    @Mock
    private Tracer tracer;

    @Mock
    private Span span;

    @Mock
    private Span.Builder spanBuilder;

    @Mock
    private Tracer.SpanInScope spanInScope;

    @Mock
    private ProceedingJoinPoint joinPoint;

    @Mock
    private MethodSignature methodSignature;

    @InjectMocks
    private UnifiedTraceAspect unifiedTraceAspect;

    private List<ConsumerRecord<String, String>> testRecords;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        ConsumerRecord<String, String> record1 = new ConsumerRecord<>("test-topic", 0, 0L, "key1", "value1");
        ConsumerRecord<String, String> record2 = new ConsumerRecord<>("test-topic", 0, 1L, "key2", "value2");
        testRecords = Arrays.asList(record1, record2);

        // 设置基本的mock行为
        when(tracer.nextSpan()).thenReturn(spanBuilder);
        when(spanBuilder.name(anyString())).thenReturn(spanBuilder);
        when(spanBuilder.start()).thenReturn(span);
        when(tracer.withSpan(span)).thenReturn(spanInScope);
        when(span.context()).thenReturn(mock(io.micrometer.tracing.TraceContext.class));
        when(span.context().traceId()).thenReturn("test-trace-id");
    }

    @Test
    void testSyncMethodWithBatchRecords() throws Throwable {
        // 准备测试方法
        Method testMethod = TestBatchConsumer.class.getMethod("processBatchSync", List.class);
        SingleTraceForBatch annotation = testMethod.getAnnotation(SingleTraceForBatch.class);

        // 设置mock
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(methodSignature.getMethod()).thenReturn(testMethod);
        when(methodSignature.getReturnType()).thenReturn(void.class);
        when(joinPoint.getArgs()).thenReturn(new Object[]{testRecords});
        when(joinPoint.proceed()).thenReturn(null);

        // 执行测试
        Object result = unifiedTraceAspect.traceBatchProcessing(joinPoint);

        // 验证结果
        assertNull(result);
        verify(tracer).nextSpan();
        verify(spanBuilder).name("test-batch-processing");
        verify(spanBuilder).start();
        verify(tracer).withSpan(span);
        verify(span).tag("batch.size", "2");
        verify(span).tag("execution.model", "batch-processing");
        verify(span).tag("consumer.type", "test");
        verify(span).end();
        verify(joinPoint).proceed();
    }

    @Test
    void testReactiveMethodWithBatchRecords() throws Throwable {
        // 准备测试方法
        Method testMethod = TestBatchConsumer.class.getMethod("processBatchReactive", List.class);

        // 设置mock
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(methodSignature.getMethod()).thenReturn(testMethod);
        when(methodSignature.getReturnType()).thenReturn(Mono.class);
        when(joinPoint.getArgs()).thenReturn(new Object[]{testRecords});
        when(joinPoint.proceed()).thenReturn(Mono.empty());
        when(tracer.currentSpan()).thenReturn(null);

        // 执行测试
        Object result = unifiedTraceAspect.traceBatchProcessing(joinPoint);

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof Mono);
        verify(tracer).nextSpan();
    }

    @Test
    void testMethodWithoutBatchRecords() throws Throwable {
        // 设置mock - 没有List参数
        when(joinPoint.getArgs()).thenReturn(new Object[]{"not-a-list"});
        when(joinPoint.proceed()).thenReturn("result");

        // 执行测试
        Object result = unifiedTraceAspect.traceBatchProcessing(joinPoint);

        // 验证结果
        assertEquals("result", result);
        verify(joinPoint).proceed();
        // 验证没有创建span
        verify(tracer, never()).nextSpan();
    }

    @Test
    void testMethodWithEmptyBatchRecords() throws Throwable {
        // 设置mock - 空的List
        when(joinPoint.getArgs()).thenReturn(new Object[]{Arrays.asList()});
        when(joinPoint.proceed()).thenReturn("result");

        // 执行测试
        Object result = unifiedTraceAspect.traceBatchProcessing(joinPoint);

        // 验证结果
        assertEquals("result", result);
        verify(joinPoint).proceed();
        // 验证没有创建span
        verify(tracer, never()).nextSpan();
    }

    /**
     * 测试用的批量消费者类
     */
    public static class TestBatchConsumer {

        @SingleTraceForBatch(spanName = "test-batch-processing", tags = {"consumer.type=test"})
        public void processBatchSync(List<ConsumerRecord<String, String>> records) {
            // 模拟同步处理
        }

        @SingleTraceForBatch(spanName = "test-reactive-processing")
        public Mono<Void> processBatchReactive(List<ConsumerRecord<String, String>> records) {
            return Mono.empty();
        }
    }
}
