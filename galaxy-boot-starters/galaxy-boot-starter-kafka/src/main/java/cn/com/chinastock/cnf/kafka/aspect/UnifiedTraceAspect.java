package cn.com.chinastock.cnf.kafka.aspect;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.kafka.annotation.SingleTraceForBatch;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;

import java.util.List;

/**
 * 批量消息处理统一链路追踪切面
 * 
 * <p>该切面用于处理标记了{@link SingleTraceForBatch}注解的方法，
 * 确保整个批量处理过程在统一的链路追踪上下文中执行。</p>
 * 
 * <p>主要功能：</p>
 * <ul>
 *     <li>为批量处理方法创建统一的Span</li>
 *     <li>支持同步和响应式（Mono/Flux）方法</li>
 *     <li>自动记录批量大小和自定义标签</li>
 *     <li>确保所有子操作（如发送消息）使用相同的traceId</li>
 * </ul>
 * 
 * <AUTHOR> Boot Team
 * @since 0.5.4
 */
@Aspect
@Component
@ConditionalOnClass({Tracer.class, Span.class})
public class UnifiedTraceAspect {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(UnifiedTraceAspect.class);

    // 检查Reactor是否在classpath中
    private static final boolean REACTOR_PRESENT = ClassUtils.isPresent("reactor.core.publisher.Mono", UnifiedTraceAspect.class.getClassLoader());

    @Autowired
    private Tracer tracer;

    /**
     * 拦截标记了@SingleTraceForBatch注解的方法
     * 
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 方法执行过程中可能抛出的异常
     */
    @Around("@annotation(SingleTraceForBatch)")
    public Object traceBatchProcessing(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        SingleTraceForBatch annotation = signature.getMethod().getAnnotation(SingleTraceForBatch.class);
        
        // 尝试从参数中获取消息列表，用于记录 batch size
        List<?> records = findRecordsArgument(joinPoint.getArgs());
        if (records == null || records.isEmpty()) {
            logger.debug(LogCategory.FRAMEWORK_LOG, "No batch records found, executing method without tracing enhancement");
            return joinPoint.proceed(); // 如果没有消息，直接执行原方法
        }

        // 判断返回类型，决定处理策略
        Class<?> returnType = signature.getReturnType();

        if (REACTOR_PRESENT && isReactiveType(returnType)) {
            // 响应式路径 (Reactive Path)
            return handleReactive(joinPoint, annotation, records);
        } else {
            // 同步路径 (Synchronous Path)
            return handleSync(joinPoint, annotation, records);
        }
    }

    /**
     * 处理同步的批量处理方法
     * 
     * @param joinPoint 连接点
     * @param annotation 注解信息
     * @param records 批量记录
     * @return 方法执行结果
     * @throws Throwable 方法执行过程中可能抛出的异常
     */
    private Object handleSync(ProceedingJoinPoint joinPoint, SingleTraceForBatch annotation, List<?> records) throws Throwable {
        Span span = tracer.nextSpan().name(annotation.spanName()).start();
        logger.debug(LogCategory.FRAMEWORK_LOG, "[Sync] Created a new span for batch processing with TraceId: {}", span.context().traceId());

        try (Tracer.SpanInScope ws = tracer.withSpan(span)) {
            // 设置标签
            setSpanTags(span, annotation, records);
            return joinPoint.proceed();
        } catch (Throwable throwable) {
            span.error(throwable);
            throw throwable;
        } finally {
            span.end();
            logger.debug(LogCategory.FRAMEWORK_LOG, "[Sync] Ended span for batch processing");
        }
    }

    /**
     * 处理返回 Mono 或 Flux 的响应式批量处理方法
     *
     * @param joinPoint 连接点
     * @param annotation 注解信息
     * @param records 批量记录
     * @return 响应式结果
     */
    private Object handleReactive(ProceedingJoinPoint joinPoint, SingleTraceForBatch annotation, List<?> records) {
        try {
            // 使用反射来处理Reactor类型，避免编译时依赖
            Class<?> monoClass = Class.forName("reactor.core.publisher.Mono");
            Object mono = monoClass.getMethod("deferContextual", java.util.function.Function.class)
                .invoke(null, (java.util.function.Function<Object, Object>) contextView -> {
                    // 从当前上下文获取或创建新的 Span
                    Span currentSpan = tracer.currentSpan();
                    Span span = currentSpan != null ?
                        tracer.nextSpan().setParent(currentSpan.context()).name(annotation.spanName()).start() :
                        tracer.nextSpan().name(annotation.spanName()).start();

                    logger.debug(LogCategory.FRAMEWORK_LOG, "[Reactive] Created a new span for batch processing with TraceId: {}", span.context().traceId());

                    try (Tracer.SpanInScope ws = tracer.withSpan(span)) {
                        // 执行原始方法，它会返回一个 Mono 或 Flux
                        Object result = joinPoint.proceed();

                        // 设置标签
                        setSpanTags(span, annotation, records);

                        // 使用反射调用doOnError和doFinally方法
                        try {
                            result = result.getClass().getMethod("doOnError", java.util.function.Consumer.class)
                                .invoke(result, (java.util.function.Consumer<Throwable>) span::error);
                            result = result.getClass().getMethod("doFinally", java.util.function.Consumer.class)
                                .invoke(result, (java.util.function.Consumer<Object>) signalType -> {
                                    span.end();
                                    logger.debug(LogCategory.FRAMEWORK_LOG, "[Reactive] Ended span for batch processing with signal: {}", signalType);
                                });
                        } catch (Exception e) {
                            logger.warn(LogCategory.FRAMEWORK_LOG, "Failed to set reactive operators, span may not be properly closed", e);
                            span.end();
                        }

                        return result;
                    } catch (Throwable e) {
                        span.error(e);
                        span.end();
                        try {
                            return monoClass.getMethod("error", Throwable.class).invoke(null, e);
                        } catch (Exception ex) {
                            throw new RuntimeException("Failed to create error Mono", ex);
                        }
                    }
                });

            // 使用反射调用contextWrite方法
            return mono.getClass().getMethod("contextWrite", java.util.function.Function.class)
                .invoke(mono, (java.util.function.Function<Object, Object>) context -> {
                    try {
                        return context.getClass().getMethod("put", Class.class, Object.class)
                            .invoke(context, Span.class, tracer.currentSpan());
                    } catch (Exception e) {
                        logger.warn(LogCategory.FRAMEWORK_LOG, "Failed to put span in context", e);
                        return context;
                    }
                });
        } catch (Exception e) {
            logger.error(LogCategory.FRAMEWORK_LOG, "Failed to handle reactive method, falling back to sync handling", e);
            // 如果反射失败，回退到同步处理
            return handleSync(joinPoint, annotation, records);
        }
    }
    
    /**
     * 设置Span标签
     * 
     * @param span Span对象
     * @param annotation 注解信息
     * @param records 批量记录
     */
    private void setSpanTags(Span span, SingleTraceForBatch annotation, List<?> records) {
        // 记录批量大小
        if (annotation.recordBatchSize()) {
            span.tag("batch.size", String.valueOf(records.size()));
        }
        
        // 设置执行模型标签
        span.tag("execution.model", "batch-processing");
        
        // 设置自定义标签
        for (String tag : annotation.tags()) {
            String[] parts = tag.split("=", 2);
            if (parts.length == 2) {
                span.tag(parts[0].trim(), parts[1].trim());
            }
        }
    }
    
    /**
     * 检查是否为响应式类型
     *
     * @param returnType 返回类型
     * @return 是否为响应式类型
     */
    private boolean isReactiveType(Class<?> returnType) {
        try {
            Class<?> monoClass = Class.forName("reactor.core.publisher.Mono");
            Class<?> fluxClass = Class.forName("reactor.core.publisher.Flux");
            return monoClass.isAssignableFrom(returnType) || fluxClass.isAssignableFrom(returnType);
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * 从方法参数中查找批量记录列表
     *
     * @param args 方法参数数组
     * @return 批量记录列表，如果未找到则返回null
     */
    private List<?> findRecordsArgument(Object[] args) {
        for (Object arg : args) {
            if (arg instanceof List) {
                return (List<?>) arg;
            }
        }
        return null;
    }
}
