package cn.com.chinastock.cnf.kafka.examples.consumer;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.kafka.examples.service.KafkaProducerService;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 批量消息消费者
 * 演示批量消息的消费处理
 */
@Component
public class BatchMessageConsumer {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(BatchMessageConsumer.class);

    private final KafkaProducerService kafkaProducerService;

    public BatchMessageConsumer(KafkaProducerService kafkaProducerService) {
        this.kafkaProducerService = kafkaProducerService;
    }

    /**
     * 批量消费消息 - 基础模式
     * 监听 batch-message-topic 主题
     * @param messages 消息列表
     */
    @KafkaListener(topics = "batch-message-topic", groupId = "batch-message-group")
    public void consumeBatchMessages(List<String> messages) {
        logger.info(LogCategory.APP_LOG, "Batch consumer received {} messages", messages.size());
        
        for (int i = 0; i < messages.size(); i++) {
            String message = messages.get(i);
            logger.info(LogCategory.APP_LOG, "Processing batch message {}/{}: {}", i + 1, messages.size(), message);
            processBatchMessage(message, i);
        }

        // 如果第一个消息不为空，那么发送一个消息至topic 'another-topic'
        if (!records.isEmpty() && records.get(0) != null) {
            logger.info(LogCategory.APP_LOG, "Sending message to another topic");
            kafkaProducerService.sendMessageToPartition("another-topic", 0, "batch-metadata", "Message from batch metadata consumer");
        }
        
        logger.info(LogCategory.APP_LOG, "Batch processing completed for {} messages", messages.size());
    }

    /**
     * 批量消费消息 - 带元数据模式
     * @param records 消费记录列表
     */
    @KafkaListener(topics = "batch-message-with-metadata-topic", groupId = "batch-metadata-group")
    public void consumeBatchMessagesWithMetadata(List<ConsumerRecord<String, String>> records) {
        logger.info(LogCategory.APP_LOG, "Batch consumer with metadata received {} records", records.size());
        
        for (int i = 0; i < records.size(); i++) {
            ConsumerRecord<String, String> record = records.get(i);
            logger.info(LogCategory.APP_LOG, 
                       "Processing batch record {}/{} - Topic: {}, Partition: {}, Offset: {}, Key: {}, Value: {}",
                       i + 1, records.size(), record.topic(), record.partition(), record.offset(), 
                       record.key(), record.value());
            
            processBatchMessage(record.value(), i);
        }

        // 如果第一个消息不为空，那么发送一个消息至topic 'another-topic'
        if (!records.isEmpty() && records.get(0) != null) {
            logger.info(LogCategory.APP_LOG, "Sending message to another topic");
            kafkaProducerService.sendMessageToPartition("another-topic", 0, "batch-metadata", "Message from batch metadata consumer");
        }
        
        logger.info(LogCategory.APP_LOG, "Batch metadata processing completed for {} records", records.size());
    }

    /**
     * 通用批量消息处理方法
     * @param message 消息内容
     * @param index 消息索引
     */
    private void processBatchMessage(String message, int index) {
        try {
            Thread.sleep(10);
            logger.debug(LogCategory.APP_LOG, "Batch message {} processed successfully: {}", index, message);
        } catch (InterruptedException e) {
            logger.error(LogCategory.EXCEPTION_LOG, "Error in batch message processing", e);
            Thread.currentThread().interrupt();
        }
    }
}
