package cn.com.chinastock.cnf.kafka.aspect;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.kafka.annotation.SingleTraceForBatch;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.TraceContext;
import io.micrometer.tracing.Tracer;
import io.micrometer.tracing.otel.bridge.OtelTraceContextBuilder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

import static cn.com.chinastock.cnf.core.log.context.TraceConstants.*;

/**
 * 批量消息处理统一链路追踪切面
 *
 * <p>该切面用于处理标记了{@link SingleTraceForBatch}注解的方法，
 * 确保整个批量处理过程在统一的链路追踪上下文中执行。</p>
 *
 * <p>主要功能：</p>
 * <ul>
 *     <li>为批量处理方法创建统一的Span</li>
 *     <li>支持同步和响应式（Mono/Flux）方法</li>
 *     <li>自动记录批量大小和自定义标签</li>
 *     <li>确保所有子操作（如发送消息）使用相同的traceId</li>
 * </ul>
 *
 * <AUTHOR> Boot Team
 * @since 0.5.4
 */
@Aspect
@Component
public class UnifiedTraceAspect {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(UnifiedTraceAspect.class);

    // 检查Reactor是否在classpath中
    private static final boolean REACTOR_PRESENT = ClassUtils.isPresent("reactor.core.publisher.Mono", UnifiedTraceAspect.class.getClassLoader());

    @Autowired(required = false)
    private Tracer tracer;

    /**
     * 拦截标记了@SingleTraceForBatch注解的方法
     *
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 方法执行过程中可能抛出的异常
     */
    @Around("@annotation(cn.com.chinastock.cnf.kafka.annotation.SingleTraceForBatch)")
    public Object traceBatchProcessing(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        SingleTraceForBatch annotation = signature.getMethod().getAnnotation(SingleTraceForBatch.class);

        logger.info(LogCategory.FRAMEWORK_LOG, "UnifiedTraceAspect: Intercepting method {} with @SingleTraceForBatch",
                signature.getMethod().getName());

        // 检查Tracer是否可用
        if (tracer == null) {
            logger.warn(LogCategory.FRAMEWORK_LOG, "UnifiedTraceAspect: Tracer not available, executing method without tracing enhancement");
            return joinPoint.proceed();
        }

        // 尝试从参数中获取消息列表，用于记录 batch size
        List<?> records = findRecordsArgument(joinPoint.getArgs());
        if (records == null || records.isEmpty()) {
            logger.info(LogCategory.FRAMEWORK_LOG, "UnifiedTraceAspect: No batch records found, executing method without tracing enhancement");
            return joinPoint.proceed(); // 如果没有消息，直接执行原方法
        }

        // 判断返回类型，决定处理策略
        Class<?> returnType = signature.getReturnType();

        if (REACTOR_PRESENT && isReactiveType(returnType)) {
            // 响应式路径 (Reactive Path)
            return handleReactive(joinPoint, annotation, records);
        } else {
            // 同步路径 (Synchronous Path)
            return handleSync(joinPoint, annotation, records);
        }
    }

    /**
     * 处理同步的批量处理方法
     *
     * @param joinPoint  连接点
     * @param annotation 注解信息
     * @param records    批量记录
     * @return 方法执行结果
     * @throws Throwable 方法执行过程中可能抛出的异常
     */
    private Object handleSync(ProceedingJoinPoint joinPoint, SingleTraceForBatch annotation, List<?> records) throws Throwable {
        Span span = tracer.currentSpan();
        if (span == null) {
            String traceId = MDC.get(TRACE_ID);
            String spanId = MDC.get(SPAN_ID);
            String parentSpanId = MDC.get(PARENT_SPAN_ID);
            span = tracer.spanBuilder().setParent(new OtelTraceContextBuilder().traceId(traceId).spanId(spanId).parentId(parentSpanId).sampled(false).build()).start();
        }
        logger.debug(LogCategory.FRAMEWORK_LOG, "[Sync] Created a new span for batch processing with TraceId: {}", span.context().traceId());

        try (Tracer.SpanInScope ws = tracer.withSpan(span)) {
            // 设置标签
            setSpanTags(span, annotation, records);
            return joinPoint.proceed();
        } catch (Throwable throwable) {
            span.error(throwable);
            throw throwable;
        } finally {
            span.end();
            logger.debug(LogCategory.FRAMEWORK_LOG, "[Sync] Ended span for batch processing");
        }
    }

    /**
     * 处理返回 Mono 或 Flux 的响应式批量处理方法
     * 注意：当前实现回退到同步处理，因为响应式支持需要Reactor依赖
     *
     * @param joinPoint  连接点
     * @param annotation 注解信息
     * @param records    批量记录
     * @return 响应式结果
     */
    private Object handleReactive(ProceedingJoinPoint joinPoint, SingleTraceForBatch annotation, List<?> records) {
        logger.warn(LogCategory.FRAMEWORK_LOG, "Reactive method detected but full reactive support is not available. " +
                "The method will be executed with basic span wrapping. " +
                "For full reactive tracing support, ensure reactor-core is available at runtime.");

        // 创建Span并执行方法
//        Span span = tracer.currentSpan();
//        if (span == null) {
//            String traceId = MDC.get(TRACE_ID);
//            String spanId = MDC.get(SPAN_ID);
//            String parentSpanId = MDC.get(PARENT_SPAN_ID);
//            span = tracer.spanBuilder().setParent(new OtelTraceContextBuilder().traceId(traceId).spanId(spanId).parentId(parentSpanId).sampled(false).build()).start();
//        }
//        logger.debug(LogCategory.FRAMEWORK_LOG, "[Reactive-Fallback] Created a new span for batch processing with TraceId: {}", span.context().traceId());

        return Mono.deferContextual(contextView -> {
            // 从 Reactor Context 中获取上游的 Tracing 信息
            Span span = contextView.getOrDefault(Span.class, tracer.currentSpan());
            if (span == null) {
                String traceId = MDC.get(TRACE_ID);
                String spanId = MDC.get(SPAN_ID);
                String parentSpanId = MDC.get(PARENT_SPAN_ID);
                span = tracer.spanBuilder().setParent(new OtelTraceContextBuilder().traceId(traceId).spanId(spanId).parentId(parentSpanId).sampled(false).build()).start();
            }

            logger.debug("[Reactive] Created a new span for batch processing with TraceId: {}", span.context().traceId());

            try {
                // 执行原始方法，它会返回一个 Mono 或 Flux
                Object result = joinPoint.proceed();

                // 使用 doOnEach/doFinally 等操作符来管理 Span 的生命周期
                if (result instanceof Mono) {
                    return ((Mono<?>) result)
                            .doOnSubscribe(subscription -> {
                            })
                            .doOnError(span::error)
                            .doFinally(signalType -> {
                                span.end();
                                logger.debug("[Reactive] Ended span for batch processing with signal: {}", signalType);
                            });
                } else { // Flux
                    return ((Flux<?>) result)
                            .doOnSubscribe(subscription -> {
                            })
                            .doOnError(span::error)
                            .doFinally(signalType -> {
                                span.end();
                                logger.debug("[Reactive] Ended span for batch processing with signal: {}", signalType);
                            });
                }
            } catch (Throwable e) {
                span.error(e);
                span.end();
                return Mono.error(e);
            }
            // 使用 contextWrite 将新创建的 Span 放入 Reactor Context，供下游（业务代码内部）使用
        }).contextWrite(context -> context.put(Span.class, tracer.currentSpan()));


//        try (Tracer.SpanInScope ws = tracer.withSpan(span)) {
//            // 设置标签
//            setSpanTags(span, annotation, records);
//
//            // 执行原始方法
//            Object result = joinPoint.proceed();
//
//            // 注意：这里无法正确管理响应式流的生命周期，Span会在方法返回时立即结束
//            // 这是一个限制，但至少确保了基本的tracing功能
//            logger.debug(LogCategory.FRAMEWORK_LOG, "[Reactive-Fallback] Method executed, span will end immediately");
//
//            return result;
//        } catch (Throwable throwable) {
//            span.error(throwable);
//            throw new RuntimeException(throwable);
//        } finally {
//            span.end();
//            logger.debug(LogCategory.FRAMEWORK_LOG, "[Reactive-Fallback] Ended span for batch processing");
//        }
    }

    /**
     * 设置Span标签
     *
     * @param span       Span对象
     * @param annotation 注解信息
     * @param records    批量记录
     */
    private void setSpanTags(Span span, SingleTraceForBatch annotation, List<?> records) {
        // 记录批量大小
        if (annotation.recordBatchSize()) {
            span.tag("batch.size", String.valueOf(records.size()));
        }

        // 设置执行模型标签
        span.tag("execution.model", "batch-processing");

        // 设置自定义标签
        for (String tag : annotation.tags()) {
            String[] parts = tag.split("=", 2);
            if (parts.length == 2) {
                span.tag(parts[0].trim(), parts[1].trim());
            }
        }
    }

    /**
     * 检查是否为响应式类型
     *
     * @param returnType 返回类型
     * @return 是否为响应式类型
     */
    private boolean isReactiveType(Class<?> returnType) {
        try {
            Class<?> monoClass = Class.forName("reactor.core.publisher.Mono");
            Class<?> fluxClass = Class.forName("reactor.core.publisher.Flux");
            return monoClass.isAssignableFrom(returnType) || fluxClass.isAssignableFrom(returnType);
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * 从方法参数中查找批量记录列表
     *
     * @param args 方法参数数组
     * @return 批量记录列表，如果未找到则返回null
     */
    private List<?> findRecordsArgument(Object[] args) {
        for (Object arg : args) {
            if (arg instanceof List) {
                return (List<?>) arg;
            }
        }
        return null;
    }
}
